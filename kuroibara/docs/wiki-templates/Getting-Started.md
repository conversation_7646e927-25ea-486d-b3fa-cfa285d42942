# 🚀 Getting Started with <PERSON><PERSON><PERSON>

Welcome to Kuroibara! This guide will help you get up and running with your modern manga management platform for discovering, organizing, and reading manga from 80+ online sources.

## 📋 Prerequisites

Before you begin, make sure you have:
- ✅ Kuroibara installed and running ([Installation Guide](Installation))
- ✅ Access to the web interface (usually `http://localhost:3000`)
- ✅ A user account created

---

## 🎯 **Step 1: Create Your Account**

1. **Navigate to Kuroibara** in your web browser
2. **Click "Register"** to create a new account
3. **Fill in your details**:
   - Username (3-50 characters)
   - Email address
   - Strong password (8+ characters)
4. **Verify your email** (if email verification is enabled)
5. **Log in** with your new credentials

### 🔐 **Optional: Enable Two-Factor Authentication**
For enhanced security, consider enabling 2FA in your profile settings.

---

## 🎨 **Step 2: Customize Your Experience**

### **Set Your Preferences**
1. **Go to Settings** (gear icon in the top navigation)
2. **Choose your theme**: Light, Dark, or System
3. **Configure NSFW settings**: Enable blur for adult content if desired
4. **Set download preferences**: Quality, path, and file formats

### **Configure Providers**
1. **Navigate to Settings → Provider Preferences**
2. **Review available providers** (80+ manga sources including MangaDex, MangaPlus, TCBScans)
3. **Enable/disable providers** based on your preferences
4. **Set favorites** for faster search results
5. **Reorder providers** by priority

---

## 🔍 **Step 3: Your First Search**

### **Search for Manga**
1. **Click the Search tab** in the main navigation
2. **Enter a manga title** (e.g., "One Piece", "Naruto")
3. **Select providers** or leave as "All" for comprehensive results
4. **Browse the results** with cover images and descriptions

### **Understanding Search Results**
- **Provider badges** show the source of each result
- **Status badges** indicate if manga is ongoing, completed, etc.
- **Cover images** provide visual identification
- **Descriptions** give you a preview of the story

---

## 📚 **Step 4: Build Your Library**

### **Add Manga to Your Library**
1. **Find a manga** you want to read
2. **Click the "+" button** on the search result card
3. **Choose a category** (optional)
4. **The manga is now in your library!**

### **Organize Your Collection**
1. **Visit the Library tab** to see your collection
2. **Create categories** (e.g., "Currently Reading", "Plan to Read")
3. **Create reading lists** for specific themes or recommendations
4. **Rate and review** manga you've read

---

## 📖 **Step 5: Start Reading**

### **Access Manga Details**
1. **Click on any manga** in your library or search results
2. **View detailed information**: description, genres, authors, status
3. **See available chapters** from different providers
4. **Check reading progress** and bookmarks

### **Reading Experience**
1. **Click on a chapter** to start reading
2. **Use navigation controls**: next/previous page, chapter navigation
3. **Bookmark your progress** automatically
4. **Switch between providers** if chapters are unavailable

---

## 📋 **Step 6: Manage Reading Lists**

### **Create Reading Lists**
1. **Go to Reading Lists** in the main navigation
2. **Click "Add Reading List"**
3. **Give it a name** (e.g., "Favorites", "Recommendations")
4. **Add manga** from your library or search results

### **Organize with Categories**
1. **Visit Categories** to create custom organization
2. **Create categories** like "Action", "Romance", "Completed"
3. **Assign manga** to multiple categories
4. **Filter your library** by category

---

## 🔧 **Step 7: Advanced Features**

### **Download for Offline Reading**
1. **Enable downloads** in settings
2. **Choose download quality** and storage location
3. **Queue chapters** for background downloading
4. **Access offline** when internet is unavailable

### **External Account Integration**
1. **Link AniList account** for sync and recommendations
2. **Connect MyAnimeList** for cross-platform tracking
3. **Import existing lists** from external services

### **Profile Customization**
1. **Upload an avatar** in profile settings
2. **Write a bio** to personalize your profile
3. **Set reading preferences** and display options

---

## 🎯 **Quick Tips for New Users**

### **🔍 Search Tips**
- Use specific titles for better results
- Try alternative spellings or romanizations
- Use the provider filter to find specific sources

### **📚 Library Management**
- Create categories before adding lots of manga
- Use reading lists for temporary collections
- Rate manga to get better recommendations

### **⚙️ Provider Management**
- Disable slow or unreliable providers
- Favorite your most trusted sources
- Check provider status in settings

### **🚀 Performance Tips**
- Limit concurrent downloads
- Clear cache periodically
- Use favorites to reduce search time

---

## 🆘 **Need Help?**

If you run into any issues:

1. **Check [Common Issues](Common-Issues)** for quick solutions
2. **Visit [Troubleshooting](Troubleshooting)** for detailed guides
3. **Search the [Wiki](Home)** for specific topics
4. **Ask in [Discussions](https://github.com/Futs/kuroibara/discussions)** for community help
5. **Report bugs** in the [Issue Tracker](https://github.com/Futs/kuroibara/issues)

---

## 🎉 **You're Ready!**

Congratulations! You now have everything you need to start enjoying Kuroibara. Happy reading! 📖

### **Next Steps**
- **[📱 User Guide](User-Guide)** - Detailed feature documentation
- **[🌐 Supported Providers](Supported-Providers)** - Explore all available sources
- **[💡 Tips & Tricks](Tips-Tricks)** - Community best practices

---

---

## 🧭 **Navigation**

**🏠 Home**: [Wiki Home](Home)
**→ Next**: [User Guide](User-Guide)
**🔧 Installation**: [Installation Guide](Installation)

---

*Need more help? Visit our [Help & Support](Getting-Help) page.*
