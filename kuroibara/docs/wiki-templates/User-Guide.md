# 📱 User Guide

Complete guide to using all Kuroibara features. This guide covers everything from basic navigation to advanced features.

---

## 🎯 **Overview**

Kuroibara is your complete manga management platform, offering:
- **🔍 Multi-provider search** across 100+ manga sources
- **📚 Personal library** with categories and reading lists
- **📖 Integrated reader** with progress tracking
- **⚙️ Customizable experience** with themes and preferences

---

## 🧭 **Navigation & Interface**

### **Main Navigation**
- **🏠 Home** - Dashboard and recent activity
- **🔍 Search** - Find manga across all providers
- **📚 Library** - Your personal manga collection
- **📋 Reading Lists** - Organized collections
- **🏷️ Categories** - Custom organization tags
- **⚙️ Settings** - Preferences and configuration

### **User Interface Elements**
- **Top Navigation Bar** - Main menu and user account
- **Search Bar** - Quick search from any page
- **Provider Badges** - Show manga source
- **Status Badges** - Ongoing, Completed, etc.
- **Progress Indicators** - Reading progress and bookmarks

---

## 🔍 **Searching for Manga**

### **Basic Search**
1. **Click Search tab** in main navigation
2. **Enter manga title** in the search box
3. **Select providers** (or leave as "All")
4. **Browse results** with cover images and descriptions

### **Advanced Search Tips**
- **Use exact titles** for better results
- **Try alternative spellings** (romanizations)
- **Search by author name** when available
- **Use genre keywords** for discovery

### **Understanding Search Results**
- **Cover Images** - Visual identification
- **Provider Badges** - Source of the manga
- **Status Badges** - Publication status
- **Descriptions** - Story summaries
- **Add Buttons** - Quick library addition

### **Provider Management**
- **Enable/disable providers** in settings
- **Set favorite providers** for priority
- **Reorder by preference** for faster results
- **Check provider status** for reliability

---

## 📚 **Managing Your Library**

### **Adding Manga**
1. **Search for manga** you want to add
2. **Click the "+" button** on search results
3. **Choose category** (optional)
4. **Manga appears in library** immediately

### **Library Organization**
- **View by grid or list** - Toggle display modes
- **Sort by title, date, status** - Organize your way
- **Filter by category** - Find specific manga
- **Search within library** - Quick filtering

### **Manga Details**
- **Click any manga** to view details
- **See description, genres, authors**
- **Check publication status**
- **View available chapters**
- **Track reading progress**

### **Reading Progress**
- **Automatic bookmarking** - Resume where you left off
- **Progress indicators** - Visual reading status
- **Chapter navigation** - Easy chapter browsing
- **Multiple provider support** - Switch sources if needed

---

## 📋 **Reading Lists & Categories**

### **Creating Reading Lists**
1. **Go to Reading Lists** tab
2. **Click "Add Reading List"**
3. **Enter name and description**
4. **Add manga from library or search**

### **Managing Reading Lists**
- **Edit list details** - Update name/description
- **Add/remove manga** - Manage contents
- **Reorder items** - Organize by preference
- **Share lists** - Export for friends (planned)

### **Using Categories**
1. **Go to Categories** tab
2. **Create custom categories** (e.g., "Action", "Romance")
3. **Assign manga to categories**
4. **Filter library by category**

### **Organization Tips**
- **Use categories for genres** - Broad classification
- **Use reading lists for themes** - Specific collections
- **Create status-based lists** - "Currently Reading", "Plan to Read"
- **Seasonal organization** - "Summer 2025 Reads"

---

## 📖 **Reading Experience**

### **Starting to Read**
1. **Click on manga** in library or search
2. **Select a chapter** from the list
3. **Choose provider** if multiple available
4. **Begin reading** with integrated viewer

### **Reader Controls**
- **Navigation arrows** - Previous/next page
- **Chapter dropdown** - Jump between chapters
- **Provider switcher** - Change source if needed
- **Bookmark button** - Save current position
- **Settings menu** - Adjust reader preferences

### **Reading Preferences**
- **Page layout** - Single page, double page, continuous
- **Reading direction** - Left-to-right, right-to-left
- **Zoom controls** - Fit width, fit height, custom zoom
- **Background color** - White, black, sepia
- **Auto-advance** - Automatic page turning (planned)

### **Progress Tracking**
- **Automatic bookmarks** - Resume reading position
- **Chapter completion** - Mark chapters as read
- **Reading history** - Track what you've read
- **Statistics** - Reading time and progress (planned)

---

## ⚙️ **Settings & Customization**

### **Account Settings**
- **Profile information** - Username, email, bio
- **Avatar upload** - Personalize your profile
- **Password change** - Security management
- **Two-factor authentication** - Enhanced security

### **Appearance Settings**
- **Theme selection** - Light, dark, system
- **NSFW content** - Enable/disable adult content
- **Blur NSFW** - Blur adult content covers
- **Language preferences** - Interface language

### **Provider Preferences**
- **Enable/disable providers** - Control search sources
- **Set favorites** - Priority providers
- **Reorder providers** - Search order preference
- **Provider status** - Check reliability

### **Reading Preferences**
- **Default reader settings** - Page layout, direction
- **Download preferences** - Quality, location
- **Notification settings** - Updates and alerts
- **Privacy settings** - Data sharing preferences

---

## 📥 **Downloads & Offline Reading**

### **Enabling Downloads**
1. **Go to Settings** → Download Preferences
2. **Enable download feature**
3. **Set download location**
4. **Choose quality settings**

### **Downloading Manga**
1. **Open manga details** page
2. **Click download button** on chapters
3. **Monitor download progress**
4. **Access offline content** in downloads section

### **Managing Downloads**
- **View download queue** - See pending downloads
- **Pause/resume downloads** - Control bandwidth
- **Delete downloaded content** - Free up space
- **Organize offline library** - Folder structure

---

## 🔗 **External Integrations**

### **AniList Integration**
1. **Go to Settings** → External Accounts
2. **Connect AniList account**
3. **Sync reading progress**
4. **Import existing lists**

### **MyAnimeList Integration**
1. **Connect MAL account** in settings
2. **Sync manga lists**
3. **Cross-platform tracking**
4. **Import/export data**

### **Data Export/Import**
- **Export library** - Backup your data
- **Import from other services** - Migrate data
- **Sync across devices** - Cloud synchronization (planned)

---

## 💡 **Tips & Best Practices**

### **Search Tips**
- **Use multiple providers** for comprehensive results
- **Try different search terms** if no results
- **Check provider status** if searches fail
- **Use favorites** for faster searches

### **Library Management**
- **Create categories early** - Organize from the start
- **Use descriptive names** - Clear category/list names
- **Regular cleanup** - Remove unwanted manga
- **Backup your data** - Export library periodically

### **Performance Tips**
- **Limit enabled providers** - Faster searches
- **Clear cache regularly** - Better performance
- **Close unused tabs** - Reduce memory usage
- **Update regularly** - Latest features and fixes

### **Privacy & Security**
- **Use strong passwords** - Secure your account
- **Enable 2FA** - Additional security
- **Review privacy settings** - Control data sharing
- **Regular security checkups** - Monitor account activity

---

## 🆘 **Getting Help**

### **Common Issues**
- **[Common Issues](Common-Issues)** - Quick solutions
- **[Troubleshooting](Troubleshooting)** - Detailed guides
- **[Performance Optimization](Performance-Optimization)** - Speed improvements

### **Support Channels**
- **[GitHub Issues](https://github.com/Futs/kuroibara/issues)** - Bug reports
- **[GitHub Discussions](https://github.com/Futs/kuroibara/discussions)** - Questions
- **[Wiki](Home)** - Documentation and guides

---

## 🧭 **Navigation**

**🏠 Home**: [Wiki Home](Home)  
**← Previous**: [Getting Started](Getting-Started)  
**→ Next**: [Managing Library](Managing-Library)

---

*Last updated: July 2025 | Need help? Visit [Getting Help](Getting-Help)*
